import { Router } from "express";
import { FaqCategoryController } from "../controllers/faq-category.controller";
import { FaqItemsController } from "../controllers/faq-items.controller";
import { FaqTranslationController } from "../controllers/faq-translation.controller";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateFaqCategorySchema,
  UpdateFaqCategorySchema,
  CreateFaqItemSchema,
  UpdateFaqItemSchema,
  CreateFaqTranslationSchema,
  UpdateFaqTranslationSchema,
  FaqIdParamSchema,
  FaqTranslationByLangParamSchema,
  FaqsByCategoryAndLanguageParamSchema,
} from "../schemas/faq.schemas";

import { AppContext } from "../types/context.types";

export function createFaqRoutes(context: AppContext): Router {
  const router = Router();

  const faqCategoryController = new FaqCategoryController(context);
  const faqItemsController = new FaqItemsController(context);
  const faqTranslationController = new FaqTranslationController(context);

  // FAQ Category routes
  router.post(
    "/faq-categories",
    validateBody(CreateFaqCategorySchema),
    faqCategoryController.create,
  );
  router.get("/faq-categories", validateQuery(PaginationQuerySchema), faqCategoryController.getAll);
  router.get("/faq-categories/:id", validateParams(UuidParamSchema), faqCategoryController.getById);
  router.put(
    "/faq-categories/:id",
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqCategorySchema),
    faqCategoryController.update,
  );
  router.delete(
    "/faq-categories/:id",
    validateParams(UuidParamSchema),
    faqCategoryController.delete,
  );

  // FAQ Items routes
  router.post("/faq-items", validateBody(CreateFaqItemSchema), faqItemsController.create);
  router.get("/faq-items", validateQuery(PaginationQuerySchema), faqItemsController.getAll);
  router.get("/faq-items/:id", validateParams(UuidParamSchema), faqItemsController.getById);
  router.put(
    "/faq-items/:id",
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqItemSchema),
    faqItemsController.update,
  );
  router.delete("/faq-items/:id", validateParams(UuidParamSchema), faqItemsController.delete);

  // FAQ Translation routes
  router.post(
    "/faq-translations",
    validateBody(CreateFaqTranslationSchema),
    faqTranslationController.createFaqTranslation,
  );
  router.put(
    "/faq-translations/:id",
    validateParams(UuidParamSchema),
    validateBody(UpdateFaqTranslationSchema),
    faqTranslationController.updateFaqTranslation,
  );
  router.get(
    "/faqs/category/:categoryId/language/:langId",
    validateParams(FaqsByCategoryAndLanguageParamSchema),
    validateQuery(PaginationQuerySchema),
    faqTranslationController.getFaqsByCategoryAndLanguage,
  );
  router.get(
    "/faq-translations/:id",
    validateParams(UuidParamSchema),
    faqTranslationController.getById,
  );
  router.get(
    "/faq/:faqId/translations",
    validateParams(FaqIdParamSchema),
    validateQuery(PaginationQuerySchema),
    faqTranslationController.getTranslationsByFaqId,
  );
  router.get(
    "/faq/:faqId/lang/:langId/translation",
    validateParams(FaqTranslationByLangParamSchema),
    faqTranslationController.getTranslationByFaqIdAndLangId,
  );

  return router;
}
