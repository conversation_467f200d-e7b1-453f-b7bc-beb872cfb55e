import { Request, Response } from "express";
import { UtteranceTranslationController } from "../controllers/utterance-translation.controller";
import { Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  parseIncludeQuery: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const {
  getPaginatedResults,
  parseIncludeQuery,
  successResponse,
  errorResponse,
} = require("@neuratalk/common");

describe("UtteranceTranslationController", () => {
  let controller: UtteranceTranslationController;
  let mockModels: Models;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockModels = {
      IntentUtterance: {
        create: jest.fn() as jest.MockedFunction<any>,
        destroy: jest.fn() as jest.MockedFunction<any>,
      },
      UtteranceTranslation: {
        create: jest.fn() as jest.MockedFunction<any>,
        findOne: jest.fn() as jest.MockedFunction<any>,
        findByPk: jest.fn() as jest.MockedFunction<any>,
        update: jest.fn() as jest.MockedFunction<any>,
        destroy: jest.fn() as jest.MockedFunction<any>,
        count: jest.fn() as jest.MockedFunction<any>,
        findAll: jest.fn() as jest.MockedFunction<any>,
      },
      Language: {},
    } as unknown as Models;

    mockContext = {
      db: {
        models: mockModels,
        getSequelize: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
        transaction: jest.fn((cb) => cb({})),
        sequelize: {} as any,
        healthCheck: jest.fn(),
      },
      rasaService: {} as any,
      botService: {} as any,
      flowService: {} as any,
    };

    controller = new UtteranceTranslationController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create new utterance and translation successfully", async () => {
      const mockUtterance = { id: "utterance-123" };
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { text: "Hello" };
      mockModels.IntentUtterance.create.mockResolvedValue(mockUtterance);
      mockModels.UtteranceTranslation.create.mockResolvedValue(mockTranslation);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.IntentUtterance.create).toHaveBeenCalledWith({
        createdBy: "user-123",
        intentId: "intent-1",
      });
      expect(mockModels.UtteranceTranslation.create).toHaveBeenCalledWith({
        utteranceId: "utterance-123",
        langId: "lang-1",
        text: "Hello",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should create translation for existing utterance successfully", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { utteranceId: "utterance-existing", text: "Hello" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);
      mockModels.UtteranceTranslation.create.mockResolvedValue(mockTranslation);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.IntentUtterance.create).not.toHaveBeenCalled();
      expect(mockModels.UtteranceTranslation.findOne).toHaveBeenCalledWith({
        where: { utteranceId: "utterance-existing", langId: "lang-1" },
      });
      expect(mockModels.UtteranceTranslation.create).toHaveBeenCalledWith({
        utteranceId: "utterance-existing",
        langId: "lang-1",
        text: "Hello",
        createdBy: "user-123",
        updatedBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 400 if translation already exists", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { utteranceId: "utterance-existing", text: "Hello" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue({});

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });

    it("should handle creation error", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      mockReq.body = { text: "Hello" };
      mockModels.IntentUtterance.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("getAll", () => {
    it("should retrieve all utterance translations", async () => {
      const mockUtterance = {
        id: "utterance-1",
        utteranceTranslations: [
          { toJSON: () => ({ id: "trans-1", text: "Text 1", langId: "lang-1" }) },
        ],
      };
      const mockPaginatedResult = {
        items: [mockUtterance],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      const mockTranslations = [
        { utteranceId: "utterance-1", langId: "lang-1", language: { name: "English", code: "en" } },
        { utteranceId: "utterance-1", langId: "lang-2", language: { name: "Spanish", code: "es" } },
      ];

      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockPaginatedResult);
      (parseIncludeQuery as jest.Mock).mockReturnValue([{}]);
      mockModels.UtteranceTranslation.findAll.mockResolvedValue(mockTranslations);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(
        mockModels.IntentUtterance,
        expect.any(Object),
        [],
        expect.any(Object),
      );
      expect(mockModels.UtteranceTranslation.findAll).toHaveBeenCalledWith(expect.any(Object));
      expect(successResponse).toHaveBeenCalledWith(
        expect.objectContaining({
          items: expect.arrayContaining([
            expect.objectContaining({
              id: "trans-1",
              text: "Text 1",
              langId: "lang-1",
              availableLanguages: expect.arrayContaining([
                expect.objectContaining({ langId: "lang-2", name: "Spanish", code: "es" }),
              ]),
            }),
          ]),
        }),
      );
    });

    it("should handle getAll error", async () => {
      mockReq.params = { intentId: "intent-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch utterance translations",
      });
    });
  });

  describe("getById", () => {
    it("should retrieve utterance translation by ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.findOne).toHaveBeenCalledWith({
        where: { id: "trans-123" },
      });
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 for non-existent utterance translation", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Utterance translation not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch utterance translation",
      });
    });
  });

  describe("getTranslationsByUtteranceId", () => {
    it("should retrieve all translations for an utterance ID", async () => {
      const mockTranslations = [{ id: "trans-1" }, { id: "trans-2" }];
      mockReq.params = { utteranceId: "utterance-123" };
      mockModels.UtteranceTranslation.findAll.mockResolvedValue(mockTranslations);

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.findAll).toHaveBeenCalledWith({
        where: { utteranceId: "utterance-123" },
      });
      expect(successResponse).toHaveBeenCalledWith(mockTranslations);
    });

    it("should return 404 if no translations found for utterance ID", async () => {
      mockReq.params = { utteranceId: "non-existent" };
      mockModels.UtteranceTranslation.findAll.mockResolvedValue([]);

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No translations found for this utterance ID",
      });
    });

    it("should handle getTranslationsByUtteranceId error", async () => {
      mockReq.params = { utteranceId: "utterance-123" };
      mockModels.UtteranceTranslation.findAll.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationsByUtteranceId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch utterance translations",
      });
    });
  });

  describe("getTranslationByUtteranceIdAndLangId", () => {
    it("should retrieve specific translation by utterance and language ID", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1", langId: "lang-1" };
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-1" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.findOne).toHaveBeenCalledWith({
        where: { utteranceId: "utterance-1", langId: "lang-1" },
      });
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 if no translation found for utterance and language ID", async () => {
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-non-existent" };
      mockModels.UtteranceTranslation.findOne.mockResolvedValue(null);

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No translation found for this utterance ID and language ID",
      });
    });

    it("should handle getTranslationByUtteranceIdAndLangId error", async () => {
      mockReq.params = { utteranceId: "utterance-1", langId: "lang-1" };
      mockModels.UtteranceTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationByUtteranceIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch utterance translation",
      });
    });
  });

  describe("update", () => {
    it("should update utterance translation successfully", async () => {
      const mockTranslation = { id: "trans-123", text: "Original Text" };
      const mockUpdatedTranslation = { id: "trans-123", text: "Updated Text" };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockResolvedValue([1]);
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockUpdatedTranslation);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.update).toHaveBeenCalledWith(
        { text: "Updated Text", updatedBy: "user-123" },
        { where: { id: "trans-123" } },
      );
      expect(successResponse).toHaveBeenCalledWith(mockUpdatedTranslation);
    });

    it("should return 404 if utterance translation not found during update", async () => {
      mockReq.params = { id: "non-existent" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockResolvedValue([0]);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Utterance translation not found",
      });
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockModels.UtteranceTranslation.update.mockRejectedValue(new Error("Update failed"));

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });

    it("should handle missing user in update request", async () => {
      const mockUpdatedTranslation = { id: "trans-123", text: "Updated Text" };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockReq.user = undefined;
      mockModels.UtteranceTranslation.update.mockResolvedValue([1]);
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockUpdatedTranslation);

      await controller.update(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.update).toHaveBeenCalledWith(
        { text: "Updated Text", updatedBy: "system" },
        { where: { id: "trans-123" } },
      );
    });
  });

  describe("delete", () => {
    it("should delete utterance translation successfully and parent IntentUtterance if no other translations exist", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.UtteranceTranslation.destroy.mockResolvedValue(1);
      mockModels.UtteranceTranslation.count.mockResolvedValue(0);
      mockModels.IntentUtterance.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.findByPk).toHaveBeenCalledWith("trans-123");
      expect(mockModels.UtteranceTranslation.destroy).toHaveBeenCalledWith({
        where: { id: "trans-123" },
      });
      expect(mockModels.UtteranceTranslation.count).toHaveBeenCalledWith({
        where: { utteranceId: "utterance-1" },
      });
      expect(mockModels.IntentUtterance.destroy).toHaveBeenCalledWith({
        where: { id: "utterance-1" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should delete utterance translation successfully but not parent IntentUtterance if other translations exist", async () => {
      const mockTranslation = { id: "trans-123", utteranceId: "utterance-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.UtteranceTranslation.destroy.mockResolvedValue(1);
      mockModels.UtteranceTranslation.count.mockResolvedValue(1);
      mockModels.IntentUtterance.destroy.not.toHaveBeenCalled();

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.UtteranceTranslation.findByPk).toHaveBeenCalledWith("trans-123");
      expect(mockModels.UtteranceTranslation.destroy).toHaveBeenCalledWith({
        where: { id: "trans-123" },
      });
      expect(mockModels.UtteranceTranslation.count).toHaveBeenCalledWith({
        where: { utteranceId: "utterance-1" },
      });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 if utterance translation not found during delete", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue(null);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Utterance translation not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.UtteranceTranslation.findByPk.mockResolvedValue({
        id: "trans-123",
        utteranceId: "utterance-1",
      });
      mockModels.UtteranceTranslation.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to delete utterance translation",
      });
    });
  });
});
