import { Request, Response } from "express";
import { BotLanguageController } from "../controllers/bot-language.controller";
import { Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("BotLanguageController", () => {
  let controller: BotLanguageController;
  let mockModels: Models;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockModels = {
      BotLanguage: {
        create: jest.fn() as jest.MockedFunction<any>,
        update: jest.fn() as jest.MockedFunction<any>,
        findOne: jest.fn() as jest.MockedFunction<any>,
        destroy: jest.fn() as jest.MockedFunction<any>,
      },
      Bot: {},
      Language: {},
      FaqCategory: {},
      FaqItems: {},
      FaqTranslation: {},
      Flow: {},
      IntentItems: {},
      IntentUtterance: {},
      UtteranceTranslation: {},
      Entities: {},
      BotModel: {},
    } as unknown as Models;

    mockContext = {
      db: { models: mockModels, getSequelize: jest.fn(), connect: jest.fn(), disconnect: jest.fn(), transaction: jest.fn((cb) => cb({})), sequelize: {} as any, healthCheck: jest.fn() },
      rasaService: {} as any,
      botService: {} as any,
      flowService: {} as any,
    };

    controller = new BotLanguageController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should assign language to bot successfully", async () => {
      const mockBotLanguage = { id: "bot-lang-123", botId: "bot-1", langId: "lang-1", isDefault: true };
      mockReq.body = { botId: "bot-1", langId: "lang-1", isDefault: true };
      mockModels.BotLanguage.create.mockResolvedValue(mockBotLanguage);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.BotLanguage.update).toHaveBeenCalledWith(
        { isDefault: false },
        { where: { botId: "bot-1", isDefault: true } },
      );
      expect(mockModels.BotLanguage.create).toHaveBeenCalledWith({
        botId: "bot-1",
        langId: "lang-1",
        isDefault: true,
        createdBy: "user-123",
      });
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockBotLanguage);
    });

    it("should handle creation error", async () => {
      mockReq.body = { botId: "bot-1", langId: "lang-1" };
      mockModels.BotLanguage.create.mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("getAll", () => {
    it("should retrieve all bot languages", async () => {
      const mockResult = {
        items: [{ id: "bot-lang-1" }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.BotLanguage, mockReq.query);
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should handle getAll error", async () => {
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to fetch bot languages",
        error: expect.any(Error),
      });
    });
  });

  describe("getById", () => {
    it("should retrieve bot language by ID", async () => {
      const mockBotLanguage = { id: "bot-lang-123" };
      mockReq.params = { id: "bot-lang-123" };
      mockModels.BotLanguage.findOne.mockResolvedValue(mockBotLanguage);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.BotLanguage.findOne).toHaveBeenCalledWith({ where: { id: "bot-lang-123" } });
      expect(successResponse).toHaveBeenCalledWith(mockBotLanguage);
    });

    it("should return 404 for non-existent bot language", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.BotLanguage.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Bot language not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "bot-lang-123" };
      mockModels.BotLanguage.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to fetch bot language",
        error: expect.any(Error),
      });
    });
  });

  describe("delete", () => {
    it("should delete bot language successfully", async () => {
      mockReq.params = { id: "bot-lang-123" };
      mockModels.BotLanguage.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.BotLanguage.destroy).toHaveBeenCalledWith({ where: { id: "bot-lang-123" } });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 for non-existent bot language", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.BotLanguage.destroy.mockResolvedValue(0);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Bot language not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "bot-lang-123" };
      mockModels.BotLanguage.destroy.mockRejectedValue(new Error("Database error"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "INTERNAL_ERROR",
        message: "Failed to unassign bot language",
        error: expect.any(Error),
      });
    });
  });
});
