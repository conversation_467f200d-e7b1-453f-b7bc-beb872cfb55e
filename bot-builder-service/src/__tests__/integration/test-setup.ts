import { DatabaseConnection } from "@neuratalk/bot-store";
import { AppContext } from "../../types/context.types";
import { App } from "../../app";
import { NextFunction, RequestHandler } from "express";

jest.mock("api_gw", () => ({
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    updateAppInfo: jest.fn(),
    getAllApps: jest.fn(),
    getAppInfo: jest.fn(),
    purgeAppInfo: jest.fn(),
    check4Blacklist: jest.fn((req, res, next: NextFunction) => next()),
    validateCreateAppInfo: jest.fn((req, res, next: NextFunction) => next()),
    checkForDuplicateName: jest.fn((req, res, next: NextFunction) => next()),
  }),
  initializeApiGw: jest.fn(),
}));

export interface TestContext {
  appInstance: App;
  app: App["app"];
  db: DatabaseConnection;
  context: AppContext;
}

export const createTestApp = async (): Promise<TestContext> => {
  process.env.DB_ENV = "test";

  const appInstance = new App();
  // Do not call appInstance.start() here, as supertest will manage the server lifecycle.
  // We only need to initialize core services and get the app context.
  await (appInstance as any).initializeCoreServices(); // Directly call private method for setup
  (appInstance as any).initializeMiddleware();
  (appInstance as any).initializeRoutes();
  (appInstance as any).initializeErrorHandling();

  const context = appInstance.getContext();
  const db = context.db;

  return { appInstance, app: appInstance.app, db, context };
};

export const createTestUser = () => ({
  id: "test-user-id",
  email: "<EMAIL>",
  name: "Test User",
});

export const mockAuthMiddleware: RequestHandler = (req, res, next) => {
  req.user = createTestUser();
  next();
};
