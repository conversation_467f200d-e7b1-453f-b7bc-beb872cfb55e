import { Request, Response } from "express";
import { FaqTranslationController } from "../controllers/faq-translation.controller";
import { DatabaseConnection, Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("FaqTranslationController", () => {
  let controller: FaqTranslationController;
  let mockModels: Models;
  let mockDb: jest.Mocked<DatabaseConnection>;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockModels = {
      FaqItems: {
        findByPk: jest.fn() as jest.MockedFunction<any>,
        create: jest.fn() as jest.MockedFunction<any>,
        destroy: jest.fn() as jest.MockedFunction<any>,
        update: jest.fn() as jest.MockedFunction<any>,
      },
      FaqTranslation: {
        create: jest.fn() as jest.MockedFunction<any>,
        findOne: jest.fn() as jest.MockedFunction<any>,
        findByPk: jest.fn() as jest.MockedFunction<any>,
        update: jest.fn() as jest.MockedFunction<any>,
        destroy: jest.fn() as jest.MockedFunction<any>,
        count: jest.fn() as jest.MockedFunction<any>,
        findAll: jest.fn() as jest.MockedFunction<any>,
      },
      Language: {},
    } as unknown as Models;

    mockDb = {
      transaction: jest.fn((cb) => cb({})),
      models: mockModels,
      sequelize: {} as any,
      healthCheck: jest.fn(),
    } as unknown as jest.Mocked<DatabaseConnection>;

    mockContext = {
      db: mockDb,
      rasaService: {} as any,
      botService: {} as any,
      flowService: {} as any,
    };

    controller = new FaqTranslationController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      headersSent: false,
    };

    jest.clearAllMocks();
  });

  describe("createFaqTranslation", () => {
    it("should create FAQ item and translation successfully", async () => {
      const mockFaqItem = { id: "faq-123", toJSON: () => ({ id: "faq-123" }) };
      const mockTranslation = { id: "trans-123", toJSON: () => ({ id: "trans-123" }) };
      mockReq.body = {
        categoryId: "cat-1",
        botId: "bot-1",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.create.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.create.mockResolvedValue(mockTranslation);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.create).toHaveBeenCalledWith(
        {
          categoryId: "cat-1",
          botId: "bot-1",
          flowId: undefined,
          createdBy: "user-123",
          updatedBy: "user-123",
        },
        expect.any(Object),
      );
      expect(mockModels.FaqTranslation.create).toHaveBeenCalledWith(
        {
          faqId: "faq-123",
          langId: "lang-1",
          questions: ["Q1"],
          answer: "A1",
          createdBy: "user-123",
          updatedBy: "user-123",
        },
        expect.any(Object),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith({
        id: "faq-123",
        translation: { id: "trans-123" },
      });
    });

    it("should add translation to existing FAQ item", async () => {
      const mockFaqItem = { id: "faq-123", toJSON: () => ({ id: "faq-123" }) };
      const mockTranslation = { id: "trans-123", toJSON: () => ({ id: "trans-123" }) };
      mockReq.body = {
        faqId: "faq-123",
        langId: "lang-2",
        questions: ["Q2"],
        answer: "A2",
      };
      mockModels.FaqItems.findByPk.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);
      mockModels.FaqTranslation.create.mockResolvedValue(mockTranslation);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockModels.FaqItems.findByPk).toHaveBeenCalledWith("faq-123", expect.any(Object));
      expect(mockModels.FaqTranslation.findOne).toHaveBeenCalledWith({
        where: { faqId: "faq-123", langId: "lang-2" },
        transaction: expect.any(Object),
      });
      expect(mockModels.FaqTranslation.create).toHaveBeenCalledWith(
        {
          faqId: "faq-123",
          langId: "lang-2",
          questions: ["Q2"],
          answer: "A2",
          createdBy: "user-123",
          updatedBy: "user-123",
        },
        expect.any(Object),
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
    });

    it("should return 400 if FAQ item not found when adding translation", async () => {
      mockReq.body = {
        faqId: "non-existent",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.findByPk.mockResolvedValue(null);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });

    it("should return 400 if translation already exists for FAQ item", async () => {
      const mockFaqItem = { id: "faq-123", toJSON: () => ({ id: "faq-123" }) };
      const existingTranslation = { id: "trans-existing" };
      mockReq.body = {
        faqId: "faq-123",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.findByPk.mockResolvedValue(mockFaqItem);
      mockModels.FaqTranslation.findOne.mockResolvedValue(existingTranslation);

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });

    it("should handle creation error", async () => {
      mockReq.body = {
        categoryId: "cat-1",
        botId: "bot-1",
        langId: "lang-1",
        questions: ["Q1"],
        answer: "A1",
      };
      mockModels.FaqItems.create.mockRejectedValue(new Error("Creation failed"));

      await controller.createFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("getFaqsByCategoryAndLanguage", () => {
    it("should retrieve FAQs by category and language", async () => {
      const mockFaqItem = {
        id: "faq-1",
        flowId: "flow-1",
        botId: "bot-1",
        categoryId: "cat-1",
        faqTranslations: [{ toJSON: () => ({ id: "trans-1", questions: ["Q1"], answer: "A1", langId: "lang-1" }) }],
      };
      const mockPaginatedResult = {
        items: [mockFaqItem],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      const mockTranslations = [
        { faqId: "faq-1", langId: "lang-1", language: { name: "English", code: "en" } },
        { faqId: "faq-1", langId: "lang-2", language: { name: "Spanish", code: "es" } },
      ];

      mockReq.params = { categoryId: "cat-1", langId: "lang-1" };
      mockReq.query = { page: "1", limit: "20" };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockPaginatedResult);
      mockModels.FaqTranslation.findAll.mockResolvedValue(mockTranslations);

      await controller.getFaqsByCategoryAndLanguage(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(
        mockModels.FaqItems,
        expect.objectContaining({
          filter: { categoryId: { eq: "cat-1" } },
        }),
        ["faqTranslations.questions", "faqTranslations.answer"],
        expect.any(Array),
      );
      expect(mockModels.FaqTranslation.findAll).toHaveBeenCalledWith(expect.any(Object));
      expect(successResponse).toHaveBeenCalledWith(expect.objectContaining({
        items: expect.arrayContaining([
          expect.objectContaining({
            faqId: "faq-1",
            questions: ["Q1"],
            answer: "A1",
            availableLanguages: expect.arrayContaining([
              expect.objectContaining({ langId: "lang-2", name: "Spanish", code: "es" }),
            ]),
          }),
        ]),
      }));
    });

    it("should handle error in getFaqsByCategoryAndLanguage", async () => {
      mockReq.params = { categoryId: "cat-1", langId: "lang-1" };
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getFaqsByCategoryAndLanguage(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch FAQs.",
      });
    });
  });

  describe("getById", () => {
    it("should retrieve FAQ translation by ID", async () => {
      const mockTranslation = { id: "trans-123" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findOne).toHaveBeenCalledWith({ where: { id: "trans-123" } });
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 for non-existent FAQ translation", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "FAQ translation not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch FAQ translation",
      });
    });
  });

  describe("getTranslationsByFaqId", () => {
    it("should retrieve all translations for a FAQ ID", async () => {
      const mockTranslations = [{ id: "trans-1" }, { id: "trans-2" }];
      mockReq.params = { faqId: "faq-123" };
      mockModels.FaqTranslation.findAll.mockResolvedValue(mockTranslations);

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findAll).toHaveBeenCalledWith({ where: { faqId: "faq-123" } });
      expect(successResponse).toHaveBeenCalledWith(mockTranslations);
    });

    it("should return 404 if no translations found for FAQ ID", async () => {
      mockReq.params = { faqId: "non-existent" };
      mockModels.FaqTranslation.findAll.mockResolvedValue([]);

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No translations found for this FAQ ID",
      });
    });

    it("should handle getTranslationsByFaqId error", async () => {
      mockReq.params = { faqId: "faq-123" };
      mockModels.FaqTranslation.findAll.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationsByFaqId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch FAQ translations",
      });
    });
  });

  describe("getTranslationByFaqIdAndLangId", () => {
    it("should retrieve specific translation by FAQ and language ID", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1", langId: "lang-1" };
      mockReq.params = { faqId: "faq-1", langId: "lang-1" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(mockTranslation);

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findOne).toHaveBeenCalledWith({
        where: { faqId: "faq-1", langId: "lang-1" },
      });
      expect(successResponse).toHaveBeenCalledWith(mockTranslation);
    });

    it("should return 404 if no translation found for FAQ and language ID", async () => {
      mockReq.params = { faqId: "faq-1", langId: "lang-non-existent" };
      mockModels.FaqTranslation.findOne.mockResolvedValue(null);

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "No translation found for this FAQ ID and language ID",
      });
    });

    it("should handle getTranslationByFaqIdAndLangId error", async () => {
      mockReq.params = { faqId: "faq-1", langId: "lang-1" };
      mockModels.FaqTranslation.findOne.mockRejectedValue(new Error("Database error"));

      await controller.getTranslationByFaqIdAndLangId(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch FAQ translation",
      });
    });
  });

  describe("updateFaqTranslation", () => {
    it("should update FAQ translation successfully", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1", toJSON: () => ({ id: "trans-123" }) };
      const mockUpdatedTranslation = { id: "trans-123", faqId: "faq-1", text: "Updated Text", toJSON: () => ({ id: "trans-123", text: "Updated Text" }) };
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text", flowId: "flow-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.update.mockResolvedValue([1]);
      mockModels.FaqItems.update.mockResolvedValue([1]);
      mockModels.FaqTranslation.findByPk.mockResolvedValueOnce(mockUpdatedTranslation);

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findByPk).toHaveBeenCalledWith("trans-123");
      expect(mockModels.FaqTranslation.update).toHaveBeenCalledWith(
        { text: "Updated Text", updatedBy: "user-123" },
        { where: { id: "trans-123" }, transaction: expect.any(Object) },
      );
      expect(mockModels.FaqItems.update).toHaveBeenCalledWith(
        { flowId: "flow-123", updatedBy: "user-123" },
        { where: { id: "faq-1" }, transaction: expect.any(Object) },
      );
      expect(successResponse).toHaveBeenCalledWith(mockUpdatedTranslation);
    });

    it("should return 404 if FAQ translation not found during update", async () => {
      mockReq.params = { id: "non-existent" };
      mockReq.body = { text: "Updated Text" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(null);

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "FAQ translation not found",
      });
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "trans-123" };
      mockReq.body = { text: "Updated Text" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue({ id: "trans-123", faqId: "faq-1" });
      mockModels.FaqTranslation.update.mockRejectedValue(new Error("Update failed"));

      await controller.updateFaqTranslation(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("delete", () => {
    it("should delete FAQ translation successfully and parent FaqItem if no other translations exist", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.destroy.mockResolvedValue(1);
      mockModels.FaqTranslation.count.mockResolvedValue(0);
      mockModels.FaqItems.destroy.mockResolvedValue(1);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findByPk).toHaveBeenCalledWith("trans-123");
      expect(mockModels.FaqTranslation.destroy).toHaveBeenCalledWith({ where: { id: "trans-123" } });
      expect(mockModels.FaqTranslation.count).toHaveBeenCalledWith({ where: { faqId: "faq-1" } });
      expect(mockModels.FaqItems.destroy).toHaveBeenCalledWith({ where: { id: "faq-1" } });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should delete FAQ translation successfully but not parent FaqItem if other translations exist", async () => {
      const mockTranslation = { id: "trans-123", faqId: "faq-1" };
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(mockTranslation);
      mockModels.FaqTranslation.destroy.mockResolvedValue(1);
      mockModels.FaqTranslation.count.mockResolvedValue(1);
      mockModels.FaqItems.destroy.not.toHaveBeenCalled();

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockModels.FaqTranslation.findByPk).toHaveBeenCalledWith("trans-123");
      expect(mockModels.FaqTranslation.destroy).toHaveBeenCalledWith({ where: { id: "trans-123" } });
      expect(mockModels.FaqTranslation.count).toHaveBeenCalledWith({ where: { faqId: "faq-1" } });
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should return 404 if FAQ translation not found during delete", async () => {
      mockReq.params = { id: "non-existent" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue(null);

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "FAQ translation not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "trans-123" };
      mockModels.FaqTranslation.findByPk.mockResolvedValue({ id: "trans-123", faqId: "faq-1" });
      mockModels.FaqTranslation.destroy.mockRejectedValue(new Error("Delete failed"));

      await controller.delete(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to delete FAQ translation",
      });
    });
  });
});
