import { App } from "../app";

// Mock external dependencies
jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    models: {},
  })),
}));

jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock("../services/bot.service", () => ({
  BotService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/flow.service", () => ({
  FlowService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../services/rasa.service", () => ({
  RasaService: jest.fn().mockImplementation(() => ({})),
}));

jest.mock("../routers/index.router", () => ({
  createRoutes: jest.fn().mockReturnValue([]),
}));

jest.mock("../middleware/auth.middleware", () => ({
  authMiddleware: jest.fn((req, res, next) => next()),
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
}));

describe("App", () => {
  let app: App;

  beforeEach(() => {
    app = new App();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("should create an App instance", () => {
    expect(app).toBeInstanceOf(App);
    expect(app.app).toBeDefined();
  });

  it("should have express app property", () => {
    expect(app.app).toBeDefined();
    expect(typeof app.app).toBe("function");
  });
});
