import { Request, Response } from "express";
import { FlowController } from "../controllers/flow.controller";
import { FlowService } from "../services/flow.service";
import { getStudioAppsService } from "api_gw";
import { CreateFlowResponse, GetFlowsResponse } from "../types";

jest.mock("../services/flow.service");
jest.mock("api_gw", () => ({
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    purgeAppInfo: jest.fn(),
  }),
}));
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));
jest.mock("../utils/api", () => ({
  getTotalPages: jest.fn((total, limit) => Math.ceil(total / limit)),
}));

const { successResponse, errorResponse } = require("@neuratalk/common");
const { getTotalPages } = require("../utils/api");

describe("FlowController", () => {
  let controller: FlowController;
  let mockFlowService: jest.Mocked<FlowService>;
  let mockStudioAppsService: any;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;

  beforeEach(() => {
    mockFlowService = new FlowService({} as any) as jest.Mocked<FlowService>;
    mockStudioAppsService = getStudioAppsService();
    controller = new FlowController(mockFlowService);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
      headersSent: false,
    };

    jest.clearAllMocks();
  });

  describe("createFlow", () => {
    it("should create a flow successfully", async () => {
      const mockFlow = { id: "flow-123", name: "Test Flow" };
      const mockAppInfo = { appId: "app-123" };
      mockReq.body = { name: "Test Flow", botId: "bot-123" };
      mockStudioAppsService.createAppInfo.mockResolvedValue(mockAppInfo);
      mockFlowService.createFlow.mockResolvedValue(mockFlow as any);

      await controller.createFlow(mockReq as any, mockRes as Response);

      expect(mockStudioAppsService.createAppInfo).toHaveBeenCalledWith(mockReq, mockRes);
      expect(mockFlowService.createFlow).toHaveBeenCalledWith(
        mockReq.body,
        "bot-123",
        "app-123",
        "user-123",
      );
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith({ flow: mockFlow, appId: "app-123" });
    });

    it("should not send response if headers already sent", async () => {
      mockReq.body = { name: "Test Flow", botId: "bot-123" };
      mockStudioAppsService.createAppInfo.mockImplementation((req: Request, res: Response) => {
        res.headersSent = true;
        return Promise.resolve({});
      });

      await controller.createFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.json).not.toHaveBeenCalled();
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "Test Flow", botId: "bot-123" };
      mockStudioAppsService.createAppInfo.mockRejectedValue(new Error("App creation failed"));

      await controller.createFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to create flow",
      });
    });
  });

  describe("getFlowById", () => {
    it("should retrieve a flow by ID", async () => {
      const mockFlow = { id: "flow-123", name: "Test Flow" };
      mockReq.params = { id: "flow-123" };
      mockFlowService.getFlowById.mockResolvedValue(mockFlow as any);

      await controller.getFlowById(mockReq as any, mockRes as Response);

      expect(mockFlowService.getFlowById).toHaveBeenCalledWith("flow-123");
      expect(successResponse).toHaveBeenCalledWith(mockFlow);
    });

    it("should return 404 if flow not found", async () => {
      mockReq.params = { id: "non-existent" };
      mockFlowService.getFlowById.mockResolvedValue(null);

      await controller.getFlowById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "FLOW_NOT_FOUND",
        message: "Flow not found",
      });
    });

    it("should handle getFlowById error", async () => {
      mockReq.params = { id: "flow-123" };
      mockFlowService.getFlowById.mockRejectedValue(new Error("Database error"));

      await controller.getFlowById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to get flow",
      });
    });
  });

  describe("updateFlow", () => {
    it("should update a flow successfully", async () => {
      const mockFlow = { id: "flow-123", name: "Updated Flow" };
      mockReq.params = { id: "flow-123" };
      mockReq.body = { name: "Updated Flow" };
      mockFlowService.updateFlow.mockResolvedValue(mockFlow as any);

      await controller.updateFlow(mockReq as any, mockRes as Response);

      expect(mockFlowService.updateFlow).toHaveBeenCalledWith(
        "flow-123",
        mockReq.body,
        "user-123",
      );
      expect(successResponse).toHaveBeenCalledWith(mockFlow);
    });

    it("should return 404 if flow not found during update", async () => {
      mockReq.params = { id: "non-existent" };
      mockReq.body = { name: "Updated Flow" };
      mockFlowService.updateFlow.mockResolvedValue(null);

      await controller.updateFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "FLOW_NOT_FOUND",
        message: "Flow not found",
      });
    });

    it("should handle update error", async () => {
      mockReq.params = { id: "flow-123" };
      mockReq.body = { name: "Updated Flow" };
      mockFlowService.updateFlow.mockRejectedValue(new Error("Update failed"));

      await controller.updateFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to update flow",
      });
    });
  });

  describe("deleteFlow", () => {
    it("should delete a flow successfully", async () => {
      mockReq.params = { id: "flow-123", appId: "app-123" };
      mockStudioAppsService.purgeAppInfo.mockResolvedValue({});
      mockFlowService.deleteFlow.mockResolvedValue(true);

      await controller.deleteFlow(mockReq as any, mockRes as Response);

      expect(mockStudioAppsService.purgeAppInfo).toHaveBeenCalledWith(mockReq, mockRes);
      expect(mockFlowService.deleteFlow).toHaveBeenCalledWith("flow-123");
      expect(mockRes.status).toHaveBeenCalledWith(204);
      expect(mockRes.send).toHaveBeenCalled();
    });

    it("should not send response if headers already sent", async () => {
      mockReq.params = { id: "flow-123", appId: "app-123" };
      mockStudioAppsService.purgeAppInfo.mockImplementation((req: Request, res: Response) => {
        res.headersSent = true;
        return Promise.resolve({});
      });

      await controller.deleteFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).not.toHaveBeenCalled();
      expect(mockRes.send).not.toHaveBeenCalled();
    });

    it("should return 404 if flow not found during delete", async () => {
      mockReq.params = { id: "non-existent", appId: "app-123" };
      mockStudioAppsService.purgeAppInfo.mockResolvedValue({});
      mockFlowService.deleteFlow.mockResolvedValue(false);

      await controller.deleteFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "FLOW_NOT_FOUND",
        message: "Flow not found",
      });
    });

    it("should handle delete error", async () => {
      mockReq.params = { id: "flow-123", appId: "app-123" };
      mockStudioAppsService.purgeAppInfo.mockRejectedValue(new Error("App purge failed"));

      await controller.deleteFlow(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to delete flow",
      });
    });
  });

  describe("getFlowsByBot", () => {
    it("should retrieve flows by bot ID with pagination", async () => {
      const mockFlows = [{ id: "flow-1", name: "Flow 1" }];
      const mockResult = { flows: mockFlows, total: 1 };
      mockReq.params = { botId: "bot-123" };
      mockReq.query = { page: "1", limit: "10", search: "Flow", isActive: "true" };
      mockFlowService.getFlowsByBot.mockResolvedValue(mockResult);

      await controller.getFlowsByBot(mockReq as any, mockRes as Response);

      expect(mockFlowService.getFlowsByBot).toHaveBeenCalledWith(
        "bot-123",
        1,
        10,
        "Flow",
        true,
      );
      expect(getTotalPages).toHaveBeenCalledWith(1, 10);
      expect(successResponse).toHaveBeenCalledWith({
        items: mockFlows,
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });
    });

    it("should handle getFlowsByBot error", async () => {
      mockReq.params = { botId: "bot-123" };
      mockFlowService.getFlowsByBot.mockRejectedValue(new Error("Database error"));

      await controller.getFlowsByBot(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to get flows",
      });
    });
  });
});
