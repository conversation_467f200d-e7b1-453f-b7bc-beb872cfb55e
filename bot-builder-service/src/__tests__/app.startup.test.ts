import { App } from "../app";
import config from "../config";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { logger } from "@neuratalk/common";

jest.mock("../config", () => ({
  __esModule: true,
  default: {
    server: {
      port: 3000,
      env: "test",
      corsOrigins: ["http://localhost:3000"],
    },
    database: {},
    security: {},
    services: {},
    logging: {},
  },
  ADMIN_USER_ID: "test-admin-id",
}));

jest.mock("@neuratalk/bot-store", () => ({
  DatabaseConnection: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    disconnect: jest.fn().mockResolvedValue(undefined),
    models: {},
  })),
}));

jest.mock("api_gw", () => ({
  initializeApiGw: jest.fn().mockResolvedValue(undefined),
  getStudioAppsService: jest.fn().mockReturnValue({
    createAppInfo: jest.fn(),
    updateAppInfo: jest.fn(),
    getAllApps: jest.fn(),
    getAppInfo: jest.fn(),
    purgeAppInfo: jest.fn(),
    check4Blacklist: jest.fn((req, res, next) => next()),
    validateCreateAppInfo: jest.fn((req, res, next) => next()),
    checkForDuplicateName: jest.fn((req, res, next) => next()),
  }),
}));

jest.mock("@neuratalk/common", () => ({
  ...jest.requireActual("@neuratalk/common"),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

describe("App Startup Simulation", () => {
  let appInstance: App;
  let mockDatabaseConnection: jest.Mocked<DatabaseConnection>;

  beforeEach(() => {
    appInstance = new App();
    mockDatabaseConnection = new DatabaseConnection() as jest.Mocked<DatabaseConnection>;
    // Manually set the db instance in the context after initialization
    (appInstance as any).context = { db: mockDatabaseConnection };
    jest.spyOn(appInstance.app, 'listen').mockImplementation((port, callback) => {
      if (callback) callback();
      return {} as any; // Mock the server object
    });
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  it("should initialize core services and start the server", async () => {
    await appInstance.start();

    expect(mockDatabaseConnection.connect).toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalledWith("Core services initialized.");
    expect(appInstance.app.listen).toHaveBeenCalledWith(config.server.port, expect.any(Function));
    expect(logger.info).toHaveBeenCalledWith(`Bot Builder Service started on port ${config.server.port}`);
  });

  it("should handle startup errors gracefully", async () => {
    mockDatabaseConnection.connect.mockRejectedValueOnce(new Error("DB connection failed"));
    const exitSpy = jest.spyOn(process, 'exit').mockImplementation((() => {}) as any);

    await appInstance.start();

    expect(mockDatabaseConnection.connect).toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith("Failed to start application:", expect.any(Error));
    expect(exitSpy).toHaveBeenCalledWith(1);

    exitSpy.mockRestore();
  });

  it("should handle graceful shutdown", async () => {
    await appInstance.start(); // Start the app first
    await appInstance.stop();

    expect(mockDatabaseConnection.disconnect).toHaveBeenCalled();
    expect(logger.info).toHaveBeenCalledWith("Shutting down Bot Builder Service...");
    expect(logger.info).toHaveBeenCalledWith("Bot Builder Service stopped");
  });

  it("should log error during shutdown", async () => {
    await appInstance.start(); // Start the app first
    mockDatabaseConnection.disconnect.mockRejectedValueOnce(new Error("DB disconnect failed"));

    await appInstance.stop();

    expect(mockDatabaseConnection.disconnect).toHaveBeenCalled();
    expect(logger.error).toHaveBeenCalledWith("Error during shutdown:", expect.any(Error));
  });
});
